# 企业运营管理平台 (WebEMS Client)

基于 Vue 3 + TypeScript + Element Plus 构建的现代化企业管理系统前端框架。

## 🎯 项目特点

- **现代化技术栈**: Vue 3 + TypeScript + Vite + Element Plus
- **响应式设计**: 完美适配桌面端和移动端
- **模块化架构**: 清晰的目录结构，易于维护和扩展
- **组件化开发**: 高度可复用的组件设计
- **类型安全**: 完整的 TypeScript 支持

## 🏗️ 项目结构

```
src/
├── components/          # 公共组件
│   ├── ModuleCard.vue  # 功能模块卡片
│   ├── SidebarNav.vue  # 左侧导航
│   └── TopBar.vue      # 顶部栏
├── layouts/            # 布局组件
│   └── MainLayout.vue  # 主布局
├── views/              # 页面组件
│   ├── Dashboard.vue   # 工作台
│   ├── Documents.vue   # 文档管理
│   └── ...            # 其他业务页面
├── router/             # 路由配置
├── stores/             # 状态管理
└── assets/             # 静态资源
```

## 🎨 界面设计

### 布局规范
- **左侧导航栏**: 88px 宽度，蓝色渐变背景
- **顶部栏**: 88px 高度，包含搜索和用户信息
- **内容区域**: 30px 内边距，响应式网格布局
- **模块卡片**: 圆角设计，悬停动效

### 主要功能模块
- 📄 文档管理
- 📊 数据分析
- 👥 人员管理
- 💰 财务管理
- 🛒 订单管理
- 📅 日程管理
- 💬 沟通协作
- 🔔 通知中心
- 📈 系统监控
- 📁 文件管理
- ⚙️ 系统设置

## 🚀 快速开始

### 环境要求
- Node.js >= 18
- Bun (推荐) 或 npm >= 8

### 安装依赖
```bash
bun install
# 或者
npm install
```

### 开发模式
```bash
bun dev
# 或者
npm run dev
```

### 构建生产版本
```bash
bun run build
# 或者
npm run build
```

### 类型检查
```bash
bun run type-check
# 或者
npm run type-check
```

### 代码检查和格式化
```bash
bun lint
# 或者
npm run lint
```

## 🔧 开发指南

### 添加新页面
1. 在 `src/views/` 目录下创建新的 Vue 组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在 `src/views/Dashboard.vue` 中添加对应的模块卡片

### 自定义主题
项目使用 Element Plus 组件库，可以通过修改 CSS 变量来自定义主题：

```css
:root {
  --el-color-primary: #4a90e2;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
}
```

### 组件开发规范
- 使用 TypeScript 进行类型定义
- 遵循 Vue 3 Composition API 规范
- 组件命名使用 PascalCase
- 样式使用 scoped 避免污染

## 📱 响应式支持

项目完全支持响应式设计：
- **桌面端**: >= 1200px
- **平板端**: 768px - 1199px
- **移动端**: < 768px

## 🛠️ 技术栈

- **框架**: Vue 3.5+
- **语言**: TypeScript 5.8+
- **构建工具**: Vite (Rolldown)
- **UI 组件**: Element Plus
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **代码规范**: ESLint + Prettier

## 📄 许可证

MIT License

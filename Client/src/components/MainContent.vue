<template>
  <div class="main-content">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 路由视图 -->
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
      
      <!-- 默认内容（当没有路由时显示） -->
      <div v-if="!$route.matched.length" class="default-content">
        <div class="welcome-section">
          <div class="welcome-icon">
            <el-icon :size="64" color="#1976d2">
              <Monitor />
            </el-icon>
          </div>
          <h2 class="welcome-title">欢迎使用 Web EMS 管理系统</h2>
          <p class="welcome-description">
            这是一个现代化的企业管理系统，提供完整的业务管理功能。
            请从左侧导航菜单选择您需要的功能模块。
          </p>
          
          <!-- 快速操作卡片 -->
          <div class="quick-actions">
            <el-card
              v-for="action in quickActions"
              :key="action.id"
              class="action-card"
              shadow="hover"
              @click="handleQuickAction(action)"
            >
              <div class="action-content">
                <div class="action-icon">
                  <el-icon :size="32" :color="action.color">
                    <component :is="action.icon" />
                  </el-icon>
                </div>
                <div class="action-info">
                  <h4 class="action-title">{{ action.title }}</h4>
                  <p class="action-desc">{{ action.description }}</p>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  Monitor,
  DataAnalysis,
  Setting,
  User,
  Document
} from '@element-plus/icons-vue'

const route = useRoute()

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title || '控制台'
})

// 快速操作数据
interface QuickAction {
  id: string
  title: string
  description: string
  icon: any
  color: string
  path?: string
}

const quickActions = ref<QuickAction[]>([
  {
    id: 'dashboard',
    title: '数据概览',
    description: '查看系统整体运行状态和关键指标',
    icon: DataAnalysis,
    color: '#1976d2',
    path: '/dashboard'
  },
  {
    id: 'users',
    title: '用户管理',
    description: '管理系统用户和权限设置',
    icon: User,
    color: '#388e3c',
    path: '/users'
  },
  {
    id: 'settings',
    title: '系统设置',
    description: '配置系统参数和功能选项',
    icon: Setting,
    color: '#f57c00',
    path: '/settings'
  },
  {
    id: 'documents',
    title: '文档中心',
    description: '查看使用手册和帮助文档',
    icon: Document,
    color: '#7b1fa2',
    path: '/documents'
  }
])

// 处理快速操作点击
const handleQuickAction = (action: QuickAction) => {
  console.log('快速操作点击:', action.title)
  // 这里可以添加路由跳转逻辑
  // if (action.path) {
  //   router.push(action.path)
  // }
}
</script>

<style scoped>
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  overflow: hidden;
}

.breadcrumb-container {
  padding: 16px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e6e8eb;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.content-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

.default-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-section {
  text-align: center;
  max-width: 800px;
  padding: 40px 20px;
}

.welcome-icon {
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.welcome-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 32px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  border: 1px solid #e6e8eb;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.action-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f8f9fa;
  border-radius: 12px;
  flex-shrink: 0;
}

.action-info {
  flex: 1;
  text-align: left;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.action-desc {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Element Plus 样式覆盖 */
:deep(.el-breadcrumb__inner) {
  color: #666;
  font-weight: 500;
}

:deep(.el-breadcrumb__inner.is-link) {
  color: #1976d2;
}

:deep(.el-breadcrumb__inner.is-link:hover) {
  color: #1565c0;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>

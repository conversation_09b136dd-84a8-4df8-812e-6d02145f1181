import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 用户角色枚举
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  PRODUCT_CENTER_MANAGER = 'product_center_manager', 
  PRODUCTION_STAFF = 'production_staff',
  OPERATOR = 'operator',
  CUSTOMER = 'customer'
}

// 角色显示名称映射
export const ROLE_NAMES = {
  [UserRole.SUPER_ADMIN]: '超管',
  [UserRole.PRODUCT_CENTER_MANAGER]: '产品中心负责人',
  [UserRole.PRODUCTION_STAFF]: '生产人员',
  [UserRole.OPERATOR]: '操作员',
  [UserRole.CUSTOMER]: '客户'
}

// 用户信息接口
export interface UserInfo {
  id: string
  name: string
  role: UserRole
  avatar?: string
}

export const useUserStore = defineStore('user', () => {
  // 当前用户信息
  const currentUser = ref<UserInfo>({
    id: '1',
    name: '管理员',
    role: UserRole.SUPER_ADMIN,
    avatar: ''
  })

  // 计算属性：当前角色显示名称
  const currentRoleName = computed(() => {
    return ROLE_NAMES[currentUser.value.role]
  })

  // 计算属性：是否为超管
  const isSuperAdmin = computed(() => {
    return currentUser.value.role === UserRole.SUPER_ADMIN
  })

  // 计算属性：是否为产品中心负责人
  const isProductCenterManager = computed(() => {
    return currentUser.value.role === UserRole.PRODUCT_CENTER_MANAGER
  })

  // 计算属性：是否为生产人员
  const isProductionStaff = computed(() => {
    return currentUser.value.role === UserRole.PRODUCTION_STAFF
  })

  // 计算属性：是否为操作员
  const isOperator = computed(() => {
    return currentUser.value.role === UserRole.OPERATOR
  })

  // 计算属性：是否为客户
  const isCustomer = computed(() => {
    return currentUser.value.role === UserRole.CUSTOMER
  })

  // 切换用户角色
  const switchRole = (role: UserRole) => {
    currentUser.value.role = role
    // 根据角色更新用户名称（演示用）
    switch (role) {
      case UserRole.SUPER_ADMIN:
        currentUser.value.name = '超级管理员'
        break
      case UserRole.PRODUCT_CENTER_MANAGER:
        currentUser.value.name = '产品中心负责人'
        break
      case UserRole.PRODUCTION_STAFF:
        currentUser.value.name = '生产人员'
        break
      case UserRole.OPERATOR:
        currentUser.value.name = '操作员'
        break
      case UserRole.CUSTOMER:
        currentUser.value.name = '客户'
        break
      default:
        currentUser.value.name = '用户'
    }
    
    console.log('角色切换至:', ROLE_NAMES[role])
  }

  // 获取所有可用角色
  const getAllRoles = () => {
    return Object.values(UserRole).map(role => ({
      value: role,
      label: ROLE_NAMES[role]
    }))
  }

  return {
    // 状态
    currentUser,
    
    // 计算属性
    currentRoleName,
    isSuperAdmin,
    isProductCenterManager,
    isProductionStaff,
    isOperator,
    isCustomer,
    
    // 方法
    switchRole,
    getAllRoles
  }
})

<template>
  <div class="role-test">
    <el-card class="test-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">角色切换测试</span>
          <el-icon class="card-icon"><User /></el-icon>
        </div>
      </template>
      
      <div class="test-content">
        <div class="current-role">
          <h3>当前角色信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">
              {{ userStore.currentUser.name }}
            </el-descriptions-item>
            <el-descriptions-item label="角色">
              {{ userStore.currentRoleName }}
            </el-descriptions-item>
            <el-descriptions-item label="角色代码">
              {{ userStore.currentUser.role }}
            </el-descriptions-item>
            <el-descriptions-item label="用户ID">
              {{ userStore.currentUser.id }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="role-permissions">
          <h3>角色权限检查</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="permission-card">
                <h4>管理权限</h4>
                <div class="permission-item">
                  <el-tag :type="userStore.isSuperAdmin ? 'success' : 'info'">
                    超级管理员: {{ userStore.isSuperAdmin ? '是' : '否' }}
                  </el-tag>
                </div>
                <div class="permission-item">
                  <el-tag :type="userStore.isProductCenterManager ? 'success' : 'info'">
                    产品中心负责人: {{ userStore.isProductCenterManager ? '是' : '否' }}
                  </el-tag>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="permission-card">
                <h4>操作权限</h4>
                <div class="permission-item">
                  <el-tag :type="userStore.isProductionStaff ? 'success' : 'info'">
                    生产人员: {{ userStore.isProductionStaff ? '是' : '否' }}
                  </el-tag>
                </div>
                <div class="permission-item">
                  <el-tag :type="userStore.isOperator ? 'success' : 'info'">
                    操作员: {{ userStore.isOperator ? '是' : '否' }}
                  </el-tag>
                </div>
                <div class="permission-item">
                  <el-tag :type="userStore.isCustomer ? 'success' : 'info'">
                    客户: {{ userStore.isCustomer ? '是' : '否' }}
                  </el-tag>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div class="quick-switch">
          <h3>快速角色切换</h3>
          <el-space wrap>
            <el-button 
              v-for="role in roleOptions" 
              :key="role.value"
              :type="userStore.currentUser.role === role.value ? 'primary' : 'default'"
              @click="handleRoleSwitch(role.value)"
            >
              {{ role.label }}
            </el-button>
          </el-space>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { User } from '@element-plus/icons-vue'
import { useUserStore, UserRole } from '@/stores/user'

// 用户状态管理
const userStore = useUserStore()

// 获取所有角色选项
const roleOptions = userStore.getAllRoles()

// 处理角色切换
const handleRoleSwitch = (role: UserRole) => {
  userStore.switchRole(role)
}
</script>

<style scoped>
.role-test {
  padding: 20px;
}

.test-card {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-icon {
  color: #409eff;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.current-role h3,
.role-permissions h3,
.quick-switch h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.permission-card {
  height: 100%;
}

.permission-card h4 {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.permission-item {
  margin-bottom: 10px;
}

.permission-item:last-child {
  margin-bottom: 0;
}
</style>

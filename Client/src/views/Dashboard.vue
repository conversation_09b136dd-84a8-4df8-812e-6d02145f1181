<template>
  <div class="dashboard">
    <!-- 第一部分：我的任务区域 -->
    <div class="tasks-section">
      <el-card class="tasks-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="card-title">我的任务</span>
            <el-icon class="card-icon"><Menu /></el-icon>
          </div>
        </template>
        <div class="tasks-content">
          <div class="tasks-left">
            <!-- 任务统计卡片 -->
            <div class="task-stats">
              <div class="task-stat-item task-stat-pending">
                <div class="task-stat-icon">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="task-stat-info">
                  <div class="task-stat-title">待我办理</div>
                  <div class="task-stat-number">0</div>
                </div>
              </div>
              <div class="task-stat-item task-stat-processing">
                <div class="task-stat-icon">
                  <el-icon><Loading /></el-icon>
                </div>
                <div class="task-stat-info">
                  <div class="task-stat-title">我已办理</div>
                  <div class="task-stat-number">0</div>
                </div>
              </div>
              <div class="task-stat-item task-stat-completed">
                <div class="task-stat-icon">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="task-stat-info">
                  <div class="task-stat-title">我发起的</div>
                  <div class="task-stat-number">0</div>
                </div>
              </div>
            </div>
          </div>
          <div class="tasks-right">
            <!-- 任务列表表格 -->
            <div class="task-table">
              <el-table :data="taskTableData" style="width: 100%" size="small">
                <el-table-column prop="sequence" label="序号" width="60" />
                <el-table-column prop="title" label="流程" width="120" />
                <el-table-column prop="status" label="流程状态" width="100" />
                <el-table-column prop="assignee" label="责任人员" width="100" />
                <el-table-column prop="deadline" label="流程期限" width="120" />
                <el-table-column prop="progress" label="流程时间" width="100" />
                <el-table-column prop="createTime" label="发起时间" width="120" />
              </el-table>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 第二部分：数据统计图表区域 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- 任务状态分布饼图 -->
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">任务状态分布</span>
              <el-icon class="card-icon"><DataAnalysis /></el-icon>
            </div>
          </template>
          <div class="chart-content">
            <div class="pie-chart-placeholder">
              <div class="pie-chart-mock">
                <div class="pie-slice pie-slice-1"></div>
                <div class="pie-slice pie-slice-2"></div>
                <div class="pie-slice pie-slice-3"></div>
                <div class="pie-center">
                  <div class="pie-total">总计</div>
                  <div class="pie-number">156</div>
                </div>
              </div>
              <div class="pie-legend">
                <div class="legend-item">
                  <span class="legend-color legend-color-1"></span>
                  <span>已完成</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color legend-color-2"></span>
                  <span>进行中</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color legend-color-3"></span>
                  <span>已分派</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 工艺任务进度统计柱状图 -->
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">工艺任务进度统计</span>
              <el-icon class="card-icon"><Document /></el-icon>
            </div>
          </template>
          <div class="chart-content">
            <div class="bar-chart-placeholder">
              <div class="bar-chart-mock">
                <div class="bar-item" v-for="(bar, index) in barData" :key="index">
                  <div class="bar-column" :style="{ height: bar.height + '%', backgroundColor: bar.color }"></div>
                  <div class="bar-label">{{ bar.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 设备使用率统计条形图 -->
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">设备使用率统计</span>
              <el-icon class="card-icon"><Setting /></el-icon>
            </div>
          </template>
          <div class="chart-content">
            <div class="horizontal-bar-placeholder">
              <div class="horizontal-bar-mock">
                <div class="h-bar-item" v-for="(item, index) in horizontalBarData" :key="index">
                  <div class="h-bar-label">{{ item.label }}</div>
                  <div class="h-bar-track">
                    <div class="h-bar-fill" :style="{ width: item.percentage + '%', backgroundColor: item.color }"></div>
                  </div>
                  <div class="h-bar-value">{{ item.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 第三部分：通知消息区域 -->
    <div class="notification-section">
      <el-card class="notification-card" shadow="never">
        <div class="notification-header">
          <div class="notification-title">
            <el-icon color="#409eff"><Bell /></el-icon>
            <span>通知消息</span>
          </div>
          <div class="notification-actions">
            <el-button type="primary" size="small">系统消息</el-button>
            <el-button size="small">业务消息</el-button>
          </div>
        </div>
        <div class="notification-content">
          <div class="notification-list">
            <div v-for="item in notifications" :key="item.id" class="notification-item">
              <el-icon :color="item.color"><component :is="item.icon" /></el-icon>
              <span class="notification-text">{{ item.message }}</span>
              <span class="notification-time">{{ item.time }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 第四部分：信息中心链接区域 -->
    <div class="info-center-section">
      <div class="info-center-links">
        <div class="info-link-item">
          <el-icon color="#409eff"><InfoFilled /></el-icon>
          <span class="info-link-text">信息中心邮箱：</span>
        </div>
        <div class="info-link-item">
          <el-icon color="#409eff"><Setting /></el-icon>
          <span class="info-link-text">信息中心电话：</span>
        </div>
        <div class="info-link-item">
          <el-icon color="#409eff"><User /></el-icon>
          <span class="info-link-text">信息中心联系人：</span>
        </div>
        <div class="info-link-item">
          <el-icon color="#409eff"><InfoFilled /></el-icon>
          <span class="info-link-text">信息中心：</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Bell,
  Warning,
  InfoFilled,
  SuccessFilled,
  DataAnalysis,
  Document,
  Setting,
  Menu,
  Clock,
  Loading,
  Check,
  User
} from '@element-plus/icons-vue'

// 通知消息数据
interface NotificationItem {
  id: string
  message: string
  time: string
  icon: any
  color: string
}

const notifications = ref<NotificationItem[]>([
  {
    id: '1',
    message: '信息中心通知：系统将于今晚22:00进行维护升级',
    time: '2小时前',
    icon: InfoFilled,
    color: '#409eff'
  },
  {
    id: '2',
    message: '信息中心通知：新版本功能已上线，请及时体验',
    time: '4小时前',
    icon: SuccessFilled,
    color: '#67c23a'
  },
  {
    id: '3',
    message: '信息中心联系人：如有问题请联系管理员',
    time: '1天前',
    icon: Warning,
    color: '#e6a23c'
  },
  {
    id: '4',
    message: '信息中心：欢迎使用一体化管控平台',
    time: '2天前',
    icon: InfoFilled,
    color: '#409eff'
  }
])

// 柱状图数据
interface BarData {
  label: string
  height: number
  color: string
}

const barData = ref<BarData[]>([
  { label: '数控', height: 85, color: '#409eff' },
  { label: '机加工', height: 90, color: '#409eff' },
  { label: '元器件', height: 75, color: '#409eff' },
  { label: 'SMT贴片', height: 65, color: '#409eff' },
  { label: '手工焊接', height: 55, color: '#67c23a' },
  { label: '总装', height: 50, color: '#67c23a' },
  { label: '包装', height: 25, color: '#e6a23c' },
  { label: '检验', height: 15, color: '#e6a23c' },
  { label: '入库', height: 80, color: '#409eff' }
])

// 水平条形图数据
interface HorizontalBarData {
  label: string
  percentage: number
  color: string
}

const horizontalBarData = ref<HorizontalBarData[]>([
  { label: '冲压ACI', percentage: 85, color: '#67c23a' },
  { label: '冲压ACI', percentage: 78, color: '#67c23a' },
  { label: '数控机', percentage: 92, color: '#e6a23c' },
  { label: '自动线体', percentage: 88, color: '#e6a23c' },
  { label: '检测设备', percentage: 65, color: '#f56c6c' }
])

// 任务表格数据
interface TaskTableData {
  sequence: number
  title: string
  status: string
  assignee: string
  deadline: string
  progress: string
  createTime: string
}

const taskTableData = ref<TaskTableData[]>([
  // 空数据，显示"暂无数据"
])
</script>

<style scoped>
.dashboard {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0;
}

/* 第一部分：我的任务区域 */
.tasks-section {
  margin-bottom: 20px;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.notification-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.notification-content {
  min-height: 120px;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.notification-time {
  font-size: 12px;
  color: #999;
}

/* 第四部分：信息中心链接区域 */
.info-center-section {
  margin-top: 20px;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.info-center-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.info-link-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.info-link-item:hover {
  color: #409eff;
}

.info-link-text {
  font-size: 14px;
}

/* 第二部分：图表区域 */
.charts-section {
  margin-bottom: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.chart-card {
  border-radius: 8px;
  border: 1px solid #e6e8eb;
  height: 320px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.card-icon {
  color: #999;
}

.chart-content {
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 饼图样式 */
.pie-chart-placeholder {
  display: flex;
  align-items: center;
  gap: 40px;
}

.pie-chart-mock {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(#ffd700 0deg 144deg, #67c23a 144deg 216deg, #409eff 216deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-center {
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.pie-total {
  font-size: 12px;
  color: #666;
}

.pie-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color-1 { background: #ffd700; }
.legend-color-2 { background: #67c23a; }
.legend-color-3 { background: #409eff; }

/* 柱状图样式 */
.bar-chart-mock {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 180px;
  padding: 20px;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.bar-column {
  width: 20px;
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

.bar-label {
  font-size: 10px;
  color: #666;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

/* 水平条形图样式 */
.horizontal-bar-mock {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  width: 100%;
}

.h-bar-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.h-bar-label {
  width: 60px;
  font-size: 12px;
  color: #666;
  text-align: right;
}

.h-bar-track {
  flex: 1;
  height: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  overflow: hidden;
}

.h-bar-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease;
}

.h-bar-value {
  width: 40px;
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 第三部分：通知消息区域 */
.notification-section {
  margin-bottom: 0;
}

.notification-card {
  border-radius: 8px;
  border: 1px solid #e6e8eb;
}

.tasks-card {
  border-radius: 8px;
  border: 1px solid #e6e8eb;
}

.tasks-content {
  display: flex;
  gap: 20px;
  height: 300px;
}

.tasks-left {
  width: 200px;
  flex-shrink: 0;
}

.task-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.task-stat-pending {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.task-stat-processing {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.task-stat-completed {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.task-stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.task-stat-info {
  flex: 1;
}

.task-stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.task-stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.tasks-right {
  flex: 1;
  overflow: hidden;
}

.task-table {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .tasks-content {
    flex-direction: column;
    height: auto;
  }

  .tasks-left {
    width: 100%;
  }

  .task-stats {
    flex-direction: row;
    gap: 16px;
  }

  .task-stat-item {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .notification-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .task-stats {
    flex-direction: column;
  }

  .info-center-links {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* Element Plus 样式覆盖 */
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>
